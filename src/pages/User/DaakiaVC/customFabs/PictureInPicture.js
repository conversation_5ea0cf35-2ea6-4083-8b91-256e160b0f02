import { useRef, useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { Track } from 'livekit-client';

// Button Components for PiP Controls
const MicButton = ({ room, className = "" }) => {
  const [isMuted, setIsMuted] = useState(false);

  useEffect(() => {
    const updateState = () => {
      const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
      setIsMuted(micTrack?.isMuted ?? true);
    };

    updateState();
    room.on('trackMuted', updateState);
    room.on('trackUnmuted', updateState);

    return () => {
      room.off('trackMuted', updateState);
      room.off('trackUnmuted', updateState);
    };
  }, [room]);

  const handleClick = () => {
    const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
    room.localParticipant.setMicrophoneEnabled(micTrack?.isMuted ?? true);
  };

  return (
    <button
      className={`pip-control-btn pip-mic-btn ${isMuted ? 'muted' : ''} ${className}`}
      onClick={handleClick}
      title="Toggle Microphone"
    >
      <svg viewBox="0 0 24 24" className="pip-control-icon">
        {isMuted ? (
          <path d="M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28zm-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18l5.98 5.99zM4.27 3L3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5.3-2.1-5.3-5.1H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c.91-.13 1.77-.45 2.54-.9L19.73 21 21 19.73 4.27 3z"/>
        ) : (
          <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
        )}
      </svg>
    </button>
  );
};

const CameraButton = ({ room, onToggle, className = "" }) => {
  const [isMuted, setIsMuted] = useState(false);

  useEffect(() => {
    const updateState = () => {
      const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
      setIsMuted(cameraTrack?.isMuted ?? true);
    };

    updateState();
    room.on('trackMuted', updateState);
    room.on('trackUnmuted', updateState);

    return () => {
      room.off('trackMuted', updateState);
      room.off('trackUnmuted', updateState);
    };
  }, [room]);

  const handleClick = () => {
    const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
    room.localParticipant.setCameraEnabled(cameraTrack?.isMuted ?? true);
    if (onToggle) onToggle();
  };

  return (
    <button
      className={`pip-control-btn pip-camera-btn ${isMuted ? 'muted' : ''} ${className}`}
      onClick={handleClick}
      title="Toggle Camera"
    >
      <svg viewBox="0 0 24 24" className="pip-control-icon">
        {isMuted ? (
          <path d="M21 6.5l-4 4V7c0-.55-.45-1-1-1H9.82L21 17.18V6.5zM3.27 2L2 3.27 4.73 6H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.21 0 .39-.08.54-.18L19.73 21 21 19.73 3.27 2zM5 16V8h1.73l8 8H5z"/>
        ) : (
          <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
        )}
      </svg>
    </button>
  );
};

const ScreenShareButton = ({ room, meetingFeatures, onToggle, className = "" }) => {
  const [isSharing, setIsSharing] = useState(false);

  useEffect(() => {
    const updateState = () => {
      const screenTrack = room.localParticipant.getTrackPublication(Track.Source.ScreenShare);
      setIsSharing(screenTrack && !screenTrack.isMuted);
    };

    updateState();
    room.on('trackPublished', updateState);
    room.on('trackUnpublished', updateState);

    return () => {
      room.off('trackPublished', updateState);
      room.off('trackUnpublished', updateState);
    };
  }, [room]);

  const handleClick = async () => {
    try {
      const screenTrack = room.localParticipant.getTrackPublication(Track.Source.ScreenShare);
      if (screenTrack && !screenTrack.isMuted) {
        await room.localParticipant.setScreenShareEnabled(false);
      } else {
        await room.localParticipant.setScreenShareEnabled(true);
      }
      if (onToggle) onToggle();
    } catch (error) {
      console.error('Screen share error:', error);
    }
  };

  if (meetingFeatures?.screen_sharing !== 1) return null;

  return (
    <button
      className={`pip-control-btn pip-screen-btn ${isSharing ? 'active' : ''} ${className}`}
      onClick={handleClick}
      title="Share Screen"
    >
      <svg viewBox="0 0 24 24" className="pip-control-icon">
        <path d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z"/>
      </svg>
    </button>
  );
};

const EndCallButton = ({ room, isElectronApp, onClose, className = "" }) => {
  const handleClick = () => {
    if (isElectronApp) {
      window?.electronAPI?.ipcRenderer?.send("stop-annotation");
    }
    room.disconnect();
    onClose();
  };

  return (
    <button
      className={`pip-control-btn pip-end-btn ${className}`}
      onClick={handleClick}
      title="End Call"
    >
      <svg viewBox="0 0 24 24" className="pip-control-icon">
        <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.7.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.***********.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.27 0-.52-.1-.7-.28-.79-.73-1.68-1.36-2.66-1.85-.33-.16-.56-.51-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
      </svg>
    </button>
  );
};

// Controls Container Component
const PipControls = ({ room, meetingFeatures, isElectronApp, onClose, onCameraToggle }) => {
  return (
    <div className="pip-controls">
      <MicButton room={room} />
      <CameraButton room={room} onToggle={onCameraToggle} />
      <ScreenShareButton room={room} meetingFeatures={meetingFeatures} onToggle={onCameraToggle} />
      <EndCallButton room={room} isElectronApp={isElectronApp} onClose={onClose} />
    </div>
  );
};

// PiP functionality as a custom hook
export const usePictureInPicture = (
  room,
  tracks,
  isTrackReference,
  generateAvatar,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  meetingFeatures,
  isElectronApp
) => {
  const pipWindowRef = useRef(null);

  // Function to update PiP content when tracks change
  const updatePipContent = () => {
    console.log('updatePipContent called');
    
    if (!pipWindowRef.current) {
      console.log('No PiP window ref');
      return;
    }

    const pipDoc = pipWindowRef.current.document;
    const pipContent = pipDoc.getElementById('pip-content');
    if (!pipContent) {
      console.log('No PiP content element found');
      return;
    }

    console.log('PiP content element found, tracks length:', tracks.length);

    // Find current track to show
    let trackToShow = null;

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      [trackToShow] = screenShareTracks;
      console.log('PiP: Showing screen share from', trackToShow.participant.name);
    } else {
      // Priority 2: Local camera
      const localCameraTracks = tracks
        .filter(isTrackReference)
        .filter((track) =>
          track.publication.source === Track.Source.Camera &&
          track.participant.isLocal
        );

      console.log('Local camera tracks found:', localCameraTracks.length);

      if (localCameraTracks.length > 0) {
        [trackToShow] = localCameraTracks;
        console.log('PiP: Showing local camera, muted:', trackToShow.publication.isMuted);
      }
    }

    // If no track, create dummy for avatar
    if (!trackToShow) {
      trackToShow = {
        participant: room.localParticipant,
        source: Track.Source.Camera,
        publication: null
      };
      console.log('PiP: Showing avatar for', trackToShow.participant.name);
    }

    console.log('Final track to show:', {
      source: trackToShow.source,
      hasPublication: !!trackToShow.publication,
      participantName: trackToShow.participant.name
    });

    // Clear existing content
    pipContent.innerHTML = '';

    // Check if we have a video track to show
    if (trackToShow.publication && trackToShow.publication.track && !trackToShow.publication.isMuted) {
      console.log('Showing video track');
      
      // Create video element
      const video = pipDoc.createElement('video');
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.style.width = '100%';
      video.style.height = '100%';
      video.style.objectFit = 'contain';
      video.style.background = '#000';
      
      // Set the MediaStream from the track
      const stream = new MediaStream([trackToShow.publication.track.mediaStreamTrack]);
      video.srcObject = stream;
      
      pipContent.appendChild(video);
      
      // Force play
      video.play().catch(e => console.log('Video play error:', e));
      
      console.log('Video element created and added to PiP');
    } else {
      // Show avatar when no video or camera is off
      console.log('Showing avatar - no video track or camera muted');
      
      const name = room.localParticipant.name || 'You';
      const avatarText = generateAvatar(name);
      
      pipContent.innerHTML = `
        <div style="
          display: flex; 
          align-items: center; 
          justify-content: center; 
          height: 100%; 
          color: white;
          text-align: center;
          background: #1a1a1a;
        ">
          <div>
            <div style="
              width: 80px;
              height: 80px;
              border-radius: 50%;
              background: #555;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 32px;
              font-weight: bold;
              margin: 0 auto 12px;
            ">${avatarText}</div>
            <div style="font-size: 14px;">${name}</div>
            <div style="font-size: 10px; opacity: 0.7; margin-top: 4px;">
              ${trackToShow.publication ? 'Camera Off' : 'No Camera'}
            </div>
          </div>
        </div>
      `;
    }

    console.log('PiP content updated');
  };

  const togglePipMode = async (enabled) => {
    // Check Document PiP support
    if (!('documentPictureInPicture' in window)) {
      setToastNotification("Document Picture-in-Picture not supported");
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    // Close existing PiP
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
      setIsPIPEnabled(false);
      return;
    }

    if (!enabled) return;

    try {
      // Create PiP window with size for controls
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: 320,
        height: 300,
      });

      pipWindowRef.current = pipWindow;
      setIsPIPEnabled(true);

      // Setup PiP document
      const pipDoc = pipWindow.document;
      
      // Add styles
      pipDoc.head.innerHTML = `
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { 
            background: #1a1a1a; 
            font-family: Arial, sans-serif; 
            overflow: hidden;
            width: 100vw;
            height: 100vh;
          }
          .pip-container {
            width: 100%;
            height: 100%;
            position: relative;
          }
          .pip-header {
            background: #2c2c2c;
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: bold;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
          }
          .pip-content {
            position: absolute;
            top: 40px;
            left: 0;
            right: 0;
            bottom: 60px;
          }
          .pip-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 8px;
            z-index: 10;
          }
          .pip-control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            color: white;
          }
          .pip-control-btn:hover {
            transform: scale(1.1);
          }
          .pip-control-btn:active {
            transform: scale(0.9);
          }
          .pip-control-btn svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
          }
          .pip-mic-btn {
            background: #4CAF50;
          }
          .pip-mic-btn.muted {
            background: #f44336;
          }
          .pip-camera-btn {
            background: #2196F3;
          }
          .pip-camera-btn.muted {
            background: #f44336;
          }
          .pip-screen-btn {
            background: #FF9800;
          }
          .pip-screen-btn.active {
            background: #4CAF50;
          }
          .pip-end-btn {
            background: #f44336;
          }
        </style>
      `;

      // Add HTML structure
      pipDoc.body.innerHTML = `
        <div class="pip-container">
          <div class="pip-header">Daakia</div>
          <div class="pip-content" id="pip-content"></div>
          <div id="pip-controls-root"></div>
        </div>
      `;

      // Render React controls
      const controlsRoot = pipDoc.getElementById('pip-controls-root');
      const reactRoot = createRoot(controlsRoot);
      reactRoot.render(
        <PipControls
          room={room}
          meetingFeatures={meetingFeatures}
          isElectronApp={isElectronApp}
          onClose={() => pipWindow.close()}
          onCameraToggle={() => setTimeout(updatePipContent, 100)}
        />
      );

      // Initial render - use updatePipContent function
      updatePipContent();

      // Handle window close
      pipWindow.addEventListener('pagehide', () => {
        pipWindowRef.current = null;
        setIsPIPEnabled(false);
      });

    } catch (error) {
      console.error('PiP error:', error);
      setToastNotification("Failed to create Picture-in-Picture");
      setToastStatus("error");
      setShowToast(true);
    }
  };

  return {
    togglePipMode,
    updatePipContent,
    pipWindowRef
  };
};
