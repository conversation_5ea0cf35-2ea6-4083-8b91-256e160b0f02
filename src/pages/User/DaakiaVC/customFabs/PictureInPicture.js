import { useRef } from 'react';
import { createRoot } from 'react-dom/client';
import PictureInPictureModal from '../components/PictureInPicture/PictureInPictureModal';

// PiP functionality now uses React components from the components folder

// Function to get compiled CSS for PiP window
const getCompiledCSS = () => {
  return `
    .pip-container {
      width: 100%;
      height: 100vh;
      background: #1a1a1a;
      color: white;
      font-family: "Inter", system-ui;
      overflow: hidden;
      position: relative;
      padding: 0;
    }

    .pip-header {
      background: #2c2c2c;
      color: white;
      padding: 8px 12px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      z-index: 10;
      height: 40px;
    }

    .pip-title {
      font-size: 14px;
      font-weight: bold;
      line-height: 24px;
    }

    .pip-content-row {
      position: absolute;
      top: 40px;
      left: 0;
      right: 0;
      bottom: 60px;
      margin: 0;
    }

    .pip-content-container {
      height: 100%;
      padding: 0;
    }

    .pip-video {
      width: 100%;
      height: 100%;
      object-fit: contain;
      background: #000;
    }

    .pip-avatar-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background: #1a1a1a;
    }

    .pip-avatar {
      text-align: center;
    }

    .pip-avatar-circle {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: #555;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      font-weight: bold;
      margin: 0 auto 12px;
      color: white;
    }

    .pip-avatar-name {
      font-size: 14px;
      color: white;
      margin-bottom: 4px;
    }

    .pip-avatar-status {
      font-size: 10px;
      opacity: 0.7;
      color: white;
    }

    .pip-controls-row {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 60px;
      margin: 0;
    }

    .pip-controls-container {
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px;
    }

    .pip-controls-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
    }

    .pip-control-btn {
      width: 36px !important;
      height: 36px !important;
      border-radius: 50% !important;
      border: none !important;
      cursor: pointer;
      display: flex !important;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      color: white !important;
      padding: 0 !important;
    }

    .pip-control-btn:hover {
      transform: scale(1.1);
      text-decoration: none !important;
    }

    .pip-control-btn:active {
      transform: scale(0.9);
    }

    .pip-control-btn:focus {
      box-shadow: none !important;
      outline: none !important;
    }

    .pip-control-icon {
      width: 16px;
      height: 16px;
      fill: currentColor;
    }

    .pip-mic-btn {
      background: #4CAF50 !important;
    }

    .pip-mic-btn.muted {
      background: #f44336 !important;
    }

    .pip-camera-btn {
      background: #2196F3 !important;
    }

    .pip-camera-btn.muted {
      background: #f44336 !important;
    }

    .pip-screen-btn {
      background: #FF9800 !important;
    }

    .pip-screen-btn.active {
      background: #4CAF50 !important;
    }

    .pip-end-btn {
      background: #f44336 !important;
    }
  `;
};

// PiP functionality as a custom hook
export const usePictureInPicture = (
  room,
  tracks,
  isTrackReference,
  generateAvatar,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  meetingFeatures,
  isElectronApp
) => {
  const pipWindowRef = useRef(null);

  // Function to update PiP content when tracks change
  const updatePipContent = () => {
    console.log('updatePipContent called - using React component, no manual DOM manipulation needed');
    // The React component will handle content updates automatically through props
  };

  const togglePipMode = async (enabled) => {
    // Check Document PiP support
    if (!('documentPictureInPicture' in window)) {
      setToastNotification("Document Picture-in-Picture not supported");
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    // Close existing PiP
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
      setIsPIPEnabled(false);
      return;
    }

    if (!enabled) return;

    try {
      // Create PiP window with size for controls
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: 320,
        height: 300,
      });

      pipWindowRef.current = pipWindow;
      setIsPIPEnabled(true);

      // Setup PiP document
      const pipDoc = pipWindow.document;

      // Add Bootstrap CSS link and custom styles
      pipDoc.head.innerHTML = `
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            background: #1a1a1a;
            font-family: "Inter", system-ui, Arial, sans-serif;
            overflow: hidden;
            width: 100vw;
            height: 100vh;
          }
          ${getCompiledCSS()}
        </style>
      `;

      // Create root element for React
      pipDoc.body.innerHTML = '<div id="pip-root"></div>';

      // Render React component
      const pipRoot = pipDoc.getElementById('pip-root');
      const reactRoot = createRoot(pipRoot);
      reactRoot.render(
        <PictureInPictureModal
          room={room}
          tracks={tracks}
          isTrackReference={isTrackReference}
          generateAvatar={generateAvatar}
          meetingFeatures={meetingFeatures}
          isElectronApp={isElectronApp}
          onClose={() => pipWindow.close()}
          onCameraToggle={() => console.log('Camera toggled in PiP')}
        />
      );

      // Handle window close
      pipWindow.addEventListener('pagehide', () => {
        pipWindowRef.current = null;
        setIsPIPEnabled(false);
      });

    } catch (error) {
      console.error('PiP error:', error);
      setToastNotification("Failed to create Picture-in-Picture");
      setToastStatus("error");
      setShowToast(true);
    }
  };

  return {
    togglePipMode,
    updatePipContent,
    pipWindowRef
  };
};
