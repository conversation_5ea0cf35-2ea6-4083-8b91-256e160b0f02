@import '../../styles/variables';

// Main PiP Container
.pip-container {
  width: 100%;
  height: 100vh;
  background: #1a1a1a;
  color: white;
  font-family: $font, system-ui;
  overflow: hidden;
  position: relative;
  padding: 0;
}

// Header Section
.pip-header {
  background: #2c2c2c;
  color: white;
  padding: 8px 12px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  height: 40px;
  
  .pip-title {
    font-size: 14px;
    font-weight: bold;
    line-height: 24px;
  }
}

// Content Section
.pip-content-row {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  bottom: 60px;
  margin: 0;
  
  .pip-content-container {
    height: 100%;
    padding: 0;
    
    .pip-video {
      width: 100%;
      height: 100%;
      object-fit: contain;
      background: #000;
    }
    
    .pip-avatar-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background: #1a1a1a;
      
      .pip-avatar {
        text-align: center;
        
        .pip-avatar-circle {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: #555;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 32px;
          font-weight: bold;
          margin: 0 auto 12px;
          color: white;
        }
        
        .pip-avatar-name {
          font-size: 14px;
          color: white;
          margin-bottom: 4px;
        }
        
        .pip-avatar-status {
          font-size: 10px;
          opacity: 0.7;
          color: white;
        }
      }
    }
  }
}

// Controls Section
.pip-controls-row {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  margin: 0;
  
  .pip-controls-container {
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    
    .pip-controls-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
    }
  }
}

// Control Buttons
.pip-control-btn {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  border: none !important;
  cursor: pointer;
  display: flex !important;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: white !important;
  padding: 0 !important;
  
  &:hover {
    transform: scale(1.1);
    text-decoration: none !important;
  }
  
  &:active {
    transform: scale(0.9);
  }
  
  &:focus {
    box-shadow: none !important;
    outline: none !important;
  }
  
  .pip-control-icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
  }
}

// Specific button styles
.pip-mic-btn {
  background: #4CAF50 !important;
  
  &.muted {
    background: #f44336 !important;
  }
}

.pip-camera-btn {
  background: #2196F3 !important;
  
  &.muted {
    background: #f44336 !important;
  }
}

.pip-screen-btn {
  background: #FF9800 !important;
  
  &.active {
    background: #4CAF50 !important;
  }
}

.pip-end-btn {
  background: #f44336 !important;
}

// Responsive adjustments
@media (max-width: 768px) {
  .pip-container {
    .pip-header {
      height: 35px;
      padding: 6px 10px;
      
      .pip-title {
        font-size: 12px;
      }
    }
    
    .pip-content-row {
      top: 35px;
      bottom: 50px;
    }
    
    .pip-controls-row {
      height: 50px;
      
      .pip-controls-container {
        padding: 6px;
        
        .pip-controls-buttons {
          gap: 6px;
        }
      }
    }
    
    .pip-control-btn {
      width: 32px !important;
      height: 32px !important;
      
      .pip-control-icon {
        width: 14px;
        height: 14px;
      }
    }
    
    .pip-avatar-container {
      .pip-avatar {
        .pip-avatar-circle {
          width: 60px;
          height: 60px;
          font-size: 24px;
          margin-bottom: 8px;
        }
        
        .pip-avatar-name {
          font-size: 12px;
        }
        
        .pip-avatar-status {
          font-size: 9px;
        }
      }
    }
  }
}

// Very small screens
@media (max-width: 480px) {
  .pip-container {
    .pip-header {
      height: 30px;
      padding: 4px 8px;
      
      .pip-title {
        font-size: 11px;
      }
    }
    
    .pip-content-row {
      top: 30px;
      bottom: 45px;
    }
    
    .pip-controls-row {
      height: 45px;
      
      .pip-controls-container {
        padding: 4px;
        
        .pip-controls-buttons {
          gap: 4px;
        }
      }
    }
    
    .pip-control-btn {
      width: 28px !important;
      height: 28px !important;
      
      .pip-control-icon {
        width: 12px;
        height: 12px;
      }
    }
    
    .pip-avatar-container {
      .pip-avatar {
        .pip-avatar-circle {
          width: 50px;
          height: 50px;
          font-size: 20px;
          margin-bottom: 6px;
        }
        
        .pip-avatar-name {
          font-size: 11px;
        }
        
        .pip-avatar-status {
          font-size: 8px;
        }
      }
    }
  }
}
