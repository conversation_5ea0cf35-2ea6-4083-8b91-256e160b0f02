import React, { useState, useEffect } from 'react';
import { Track } from 'livekit-client';
import { Container, Row, Col } from 'react-bootstrap';

function PipContent({ room, tracks, isTrackReference, generateAvatar }) {
  const [currentTrack, setCurrentTrack] = useState(null);

  useEffect(() => {
    const updateContent = () => {
      console.log('PipContent: updateContent called, tracks length:', tracks.length);
      
      // Find current track to show
      let trackToShow = null;

      // Priority 1: Screen share (any participant)
      const screenShareTracks = tracks
        .filter(isTrackReference)
        .filter((track) => track.publication.source === Track.Source.ScreenShare);

      if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
        [trackToShow] = screenShareTracks;
        console.log('PipContent: Showing screen share from', trackToShow.participant.name);
      } else {
        // Priority 2: Local camera
        const localCameraTracks = tracks
          .filter(isTrackReference)
          .filter((track) =>
            track.publication.source === Track.Source.Camera &&
            track.participant.isLocal
          );

        console.log('PipContent: Local camera tracks found:', localCameraTracks.length);

        if (localCameraTracks.length > 0) {
          [trackToShow] = localCameraTracks;
          console.log('PipContent: Showing local camera, muted:', trackToShow.publication.isMuted);
        }
      }

      // If no track, create dummy for avatar
      if (!trackToShow) {
        trackToShow = {
          participant: room.localParticipant,
          source: Track.Source.Camera,
          publication: null
        };
        console.log('PipContent: Showing avatar for', trackToShow.participant.name);
      }

      setCurrentTrack(trackToShow);
    };

    updateContent();
    
    // Listen for track changes
    const handleTrackUpdate = () => {
      setTimeout(updateContent, 100);
    };

    room.on('trackPublished', handleTrackUpdate);
    room.on('trackUnpublished', handleTrackUpdate);
    room.on('trackMuted', handleTrackUpdate);
    room.on('trackUnmuted', handleTrackUpdate);

    return () => {
      room.off('trackPublished', handleTrackUpdate);
      room.off('trackUnpublished', handleTrackUpdate);
      room.off('trackMuted', handleTrackUpdate);
      room.off('trackUnmuted', handleTrackUpdate);
    };
  }, [room, tracks, isTrackReference]);

  const renderVideoContent = () => {
    if (!currentTrack) return null;

    // Check if we have a video track to show
    if (currentTrack.publication && currentTrack.publication.track && !currentTrack.publication.isMuted) {
      return (
        <VideoElement track={currentTrack.publication.track} />
      );
    } else {
      // Show avatar when no video or camera is off
      const name = room.localParticipant.name || 'You';
      const avatarText = generateAvatar(name);
      
      return (
        <div className="pip-avatar-container">
          <div className="pip-avatar">
            <div className="pip-avatar-circle">
              {avatarText}
            </div>
            <div className="pip-avatar-name">{name}</div>
            <div className="pip-avatar-status">
              {currentTrack.publication ? 'Camera Off' : 'No Camera'}
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <Container fluid className="pip-content-container">
      <Row className="h-100">
        <Col className="d-flex align-items-center justify-content-center">
          {renderVideoContent()}
        </Col>
      </Row>
    </Container>
  );
}

// Video element component
function VideoElement({ track }) {
  const videoRef = React.useRef(null);

  useEffect(() => {
    if (videoRef.current && track) {
      const stream = new MediaStream([track.mediaStreamTrack]);
      videoRef.current.srcObject = stream;
      videoRef.current.play().catch(e => console.log('Video play error:', e));
    }
  }, [track]);

  return (
    <video
      ref={videoRef}
      autoPlay
      playsInline
      muted
      className="pip-video"
    />
  );
}

export default PipContent;
