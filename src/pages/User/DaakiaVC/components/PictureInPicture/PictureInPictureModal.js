import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import PipContent from './PipContent.js';
import PipControls from './PipControls.js';

function PictureInPictureModal({ 
  room, 
  tracks, 
  isTrackReference, 
  generateAvatar, 
  meetingFeatures, 
  isElectronApp, 
  onClose, 
  onCameraToggle 
}) {
  return (
    <Container fluid className="pip-container">
      {/* Header */}
      <Row className="pip-header">
        <Col>
          <div className="pip-title">Daakia</div>
        </Col>
      </Row>

      {/* Content Area */}
      <Row className="pip-content-row">
        <Col>
          <PipContent 
            room={room}
            tracks={tracks}
            isTrackReference={isTrackReference}
            generateAvatar={generateAvatar}
          />
        </Col>
      </Row>

      {/* Controls */}
      <Row className="pip-controls-row">
        <Col>
          <PipControls
            room={room}
            meetingFeatures={meetingFeatures}
            isElectronApp={isElectronApp}
            onClose={onClose}
            onCameraToggle={onCameraToggle}
          />
        </Col>
      </Row>
    </Container>
  );
}

export default PictureInPictureModal;
